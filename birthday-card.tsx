"use client"

import { useState, useEffect, useRef } from "react"
import { Play, Pause, Volume2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function BirthdayCard() {
  const [currentImage, setCurrentImage] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const audioRef = useRef<HTMLAudioElement>(null)

  const images = [
    "/images/img1.png",
    "/images/img2.png",
    "/images/img3.png",
    "/images/img4.png",
    "/images/img5.png",
    "/images/img6.png",
  ]

  // Auto-slide images every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImage((prev) => (prev + 1) % images.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [images.length])

  const toggleAudio = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-200 via-purple-200 to-yellow-200 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-white/90 backdrop-blur-sm shadow-2xl border-4 border-yellow-300">
        <CardContent className="p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-yellow-500 bg-clip-text text-transparent mb-4">
              🎉 HAPPY 31st BIRTHDAY! 🎉
            </h1>
            <p className="text-2xl text-gray-700 font-semibold">{"Hope your special day is amazing!"}</p>
          </div>

          {/* Image Slider */}
          <div className="relative mb-8">
            <div className="aspect-video rounded-xl overflow-hidden shadow-lg border-4 border-rainbow bg-gradient-to-r from-pink-300 to-purple-300 p-1">
              <div className="w-full h-full rounded-lg overflow-hidden relative">
                {images.map((src, index) => (
                  <img
                    key={index}
                    src={src || "/placeholder.svg"}
                    alt={`Birthday image ${index + 1}`}
                    className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-1000 ${
                      index === currentImage ? "opacity-100" : "opacity-0"
                    }`}
                  />
                ))}

                {/* Image counter */}
                <div className="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {currentImage + 1} / {images.length}
                </div>
              </div>
            </div>

            {/* Image dots indicator */}
            <div className="flex justify-center mt-4 space-x-2">
              {images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImage(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentImage ? "bg-purple-500 scale-125" : "bg-gray-300 hover:bg-gray-400"
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Audio Controls */}
          <div className="text-center mb-8">
            <Button
              onClick={toggleAudio}
              size="lg"
              className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white font-bold py-4 px-8 rounded-full shadow-lg transform hover:scale-105 transition-all duration-200"
            >
              {isPlaying ? (
                <>
                  <Pause className="mr-2 h-5 w-5" />
                  Pause Birthday Song
                </>
              ) : (
                <>
                  <Play className="mr-2 h-5 w-5" />
                  Play Birthday Song
                </>
              )}
              <Volume2 className="ml-2 h-5 w-5" />
            </Button>
          </div>

          {/* Birthday Message */}
          <div className="text-center bg-gradient-to-r from-yellow-100 to-pink-100 rounded-xl p-6 border-2 border-yellow-300">
            <p className="text-xl text-gray-800 leading-relaxed font-medium">
              🎂 {"Wishing you a year filled with happiness, laughter, and all your favorite things!"} 🎈
            </p>
            <p className="text-lg text-gray-600 mt-4">{"May this new chapter of your life be your best one yet!"} ✨</p>
          </div>

          {/* Decorative elements */}
          <div className="flex justify-center mt-6 text-4xl space-x-4">
            <span className="animate-bounce">🎁</span>
            <span className="animate-bounce delay-100">🎊</span>
            <span className="animate-bounce delay-200">🎉</span>
            <span className="animate-bounce delay-300">🎈</span>
            <span className="animate-bounce delay-400">🎂</span>
          </div>

          {/* Hidden audio element */}
          <audio ref={audioRef} src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/happy-birthday-02f1pqwb1q3CGa4PeSB0grnydt50aB.mp3" onEnded={() => setIsPlaying(false)} loop />
        </CardContent>
      </Card>
    </div>
  )
}
