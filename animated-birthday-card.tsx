"use client"

import { useState, useEffect, useRef } from "react"
import { Play, Pause, Volume2, Heart } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface Confetti {
  id: number
  x: number
  y: number
  color: string
  rotation: number
  speed: number
}

interface Balloon {
  id: number
  x: number
  y: number
  color: string
  size: number
  floatSpeed: number
}

export default function AnimatedBirthdayCard() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [confetti, setConfetti] = useState<Confetti[]>([])
  const [balloons, setBalloons] = useState<Balloon[]>([])
  const [showCelebration, setShowCelebration] = useState(false)
  const audioRef = useRef<HTMLAudioElement>(null)
  const confettiRef = useRef<number>(0)

  const colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#98D8C8"]

  // Initialize balloons
  useEffect(() => {
    const initialBalloons: Balloon[] = Array.from({ length: 8 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: 100 + Math.random() * 50,
      color: colors[Math.floor(Math.random() * colors.length)],
      size: 40 + Math.random() * 20,
      floatSpeed: 0.5 + Math.random() * 1,
    }))
    setBalloons(initialBalloons)
  }, [])

  // Animate balloons
  useEffect(() => {
    const interval = setInterval(() => {
      setBalloons((prev) =>
        prev
          .map((balloon) => ({
            ...balloon,
            y: balloon.y - balloon.floatSpeed,
            x: balloon.x + Math.sin(Date.now() * 0.001 + balloon.id) * 0.5,
          }))
          .map((balloon) => (balloon.y < -20 ? { ...balloon, y: 120, x: Math.random() * 100 } : balloon)),
      )
    }, 50)
    return () => clearInterval(interval)
  }, [])

  // Confetti animation
  useEffect(() => {
    if (showCelebration) {
      const interval = setInterval(() => {
        setConfetti((prev) => {
          const newConfetti = Array.from({ length: 5 }, (_, i) => ({
            id: confettiRef.current++,
            x: Math.random() * 100,
            y: -10,
            color: colors[Math.floor(Math.random() * colors.length)],
            rotation: Math.random() * 360,
            speed: 2 + Math.random() * 3,
          }))

          const updatedConfetti = [...prev, ...newConfetti]
            .map((piece) => ({
              ...piece,
              y: piece.y + piece.speed,
              rotation: piece.rotation + 5,
            }))
            .filter((piece) => piece.y < 120)

          return updatedConfetti
        })
      }, 100)

      const timeout = setTimeout(() => {
        setShowCelebration(false)
        setConfetti([])
      }, 5000)

      return () => {
        clearInterval(interval)
        clearTimeout(timeout)
      }
    }
  }, [showCelebration])

  const toggleAudio = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play().catch((error) => {
          console.error("Audio playback failed:", error)
          setIsPlaying(false)
        })
        setShowCelebration(true)
      }
      setIsPlaying(!isPlaying)
    }
  }

  const triggerCelebration = () => {
    setShowCelebration(true)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-300 via-purple-300 to-blue-300 flex items-center justify-center p-2 sm:p-4 overflow-hidden relative">
      {/* Floating Balloons */}
      {balloons.map((balloon) => (
        <div
          key={balloon.id}
          className="absolute pointer-events-none animate-pulse hidden sm:block"
          style={{
            left: `${balloon.x}%`,
            top: `${balloon.y}%`,
            transform: "translateX(-50%)",
            zIndex: 1,
          }}
        >
          <div
            className="rounded-full shadow-lg"
            style={{
              width: `${Math.max(balloon.size * 0.7, 25)}px`,
              height: `${Math.max(balloon.size * 1.2 * 0.7, 30)}px`,
              backgroundColor: balloon.color,
              animation: `float 3s ease-in-out infinite ${balloon.id * 0.5}s`,
            }}
          />
          <div className="w-0.5 h-4 sm:h-8 bg-gray-600 mx-auto" style={{ marginTop: "-2px" }} />
        </div>
      ))}

      {/* Falling Confetti */}
      {confetti.map((piece) => (
        <div
          key={piece.id}
          className="absolute w-3 h-3 pointer-events-none"
          style={{
            left: `${piece.x}%`,
            top: `${piece.y}%`,
            backgroundColor: piece.color,
            transform: `rotate(${piece.rotation}deg)`,
            zIndex: 10,
          }}
        />
      ))}

      <Card className="w-full max-w-sm sm:max-w-2xl lg:max-w-3xl bg-white/95 backdrop-blur-sm shadow-2xl border-2 sm:border-4 border-yellow-400 relative z-20 animate-bounce-in mx-2">
        <CardContent className="p-4 sm:p-6 lg:p-8">
          {/* Animated Header */}
          <div className="text-center mb-6 sm:mb-8 relative">
            <div className="animate-pulse absolute -top-2 sm:-top-4 -left-2 sm:-left-4 text-2xl sm:text-4xl">🎊</div>
            <div className="animate-pulse absolute -top-2 sm:-top-4 -right-2 sm:-right-4 text-2xl sm:text-4xl">🎉</div>

            <h1 className="text-3xl sm:text-5xl lg:text-7xl font-bold mb-4 animate-bounce">
              <span className="bg-gradient-to-r from-pink-500 via-purple-500 to-yellow-500 bg-clip-text text-transparent animate-gradient">
                🎂 HAPPY 31st BIRTHDAY! 🎂
              </span>
            </h1>

            <div className="flex justify-center items-center space-x-2 animate-fade-in-up">
              <Heart className="text-red-500 animate-pulse" size={16} />
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-700 font-semibold px-2">
                {"Hope your day is magical!"}
              </p>
              <Heart className="text-red-500 animate-pulse" size={16} />
            </div>
          </div>

          {/* Decorative Birthday Elements */}
          <div className="text-center mb-6 sm:mb-8">
            <div className="bg-gradient-to-r from-pink-400 via-purple-400 to-yellow-400 rounded-xl p-4 sm:p-6 lg:p-8 shadow-2xl transform hover:scale-105 transition-transform duration-300">
              <div className="text-4xl sm:text-6xl lg:text-8xl mb-2 sm:mb-4 animate-bounce">🎂</div>
              <div className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-1 sm:mb-2 animate-pulse">31</div>
              <div className="text-lg sm:text-xl lg:text-2xl text-white font-semibold">Years of Awesome!</div>
            </div>
          </div>

          {/* Animated Audio Controls */}
          <div className="text-center mb-6 sm:mb-8">
            <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4">
              <Button
                onClick={toggleAudio}
                size="lg"
                className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white font-bold py-3 sm:py-4 px-4 sm:px-8 rounded-full shadow-xl transform hover:scale-110 transition-all duration-200 animate-pulse-slow text-sm sm:text-base"
              >
                {isPlaying ? (
                  <>
                    <Pause className="mr-2 h-4 w-4 sm:h-6 sm:w-6 animate-spin" />
                    <span className="hidden sm:inline">Pause Birthday Song</span>
                    <span className="sm:hidden">Pause Song</span>
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4 sm:h-6 sm:w-6" />
                    <span className="hidden sm:inline">🎵 Play Birthday Song 🎵</span>
                    <span className="sm:hidden">🎵 Play Song 🎵</span>
                  </>
                )}
                <Volume2 className="ml-2 h-4 w-4 sm:h-6 sm:w-6 animate-bounce" />
              </Button>

              <Button
                onClick={triggerCelebration}
                size="lg"
                className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-bold py-3 sm:py-4 px-4 sm:px-6 rounded-full shadow-xl transform hover:scale-110 transition-all duration-200 text-sm sm:text-base"
              >
                🎊 Celebrate! 🎊
              </Button>
            </div>
          </div>

          {/* Animated Birthday Message */}
          <div className="text-center bg-gradient-to-r from-yellow-100 via-pink-100 to-purple-100 rounded-xl p-4 sm:p-6 lg:p-8 border-2 sm:border-4 border-yellow-400 shadow-lg animate-fade-in-up relative overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pink-400 via-purple-400 to-yellow-400 animate-shimmer"></div>

            <p className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-bold mb-3 sm:mb-4 animate-typewriter">
              🎈 {"Wishing you a year filled with happiness, laughter, and endless joy!"} 🎈
            </p>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 animate-fade-in-delayed">
              {"May this new chapter bring you amazing adventures and beautiful memories!"} ✨🌟✨
            </p>
          </div>

          {/* Dancing Emojis */}
          <div className="flex justify-center mt-6 sm:mt-8 text-3xl sm:text-4xl lg:text-5xl space-x-3 sm:space-x-6">
            <span className="animate-bounce delay-0">🎁</span>
            <span className="animate-bounce delay-100">🎊</span>
            <span className="animate-bounce delay-200">🎉</span>
            <span className="animate-bounce delay-300">🎈</span>
            <span className="animate-bounce delay-400">🎂</span>
            <span className="animate-bounce delay-500">🥳</span>
          </div>

          <audio ref={audioRef} src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/happy-birthday-02f1pqwb1q3CGa4PeSB0grnydt50aB.mp3" onEnded={() => setIsPlaying(false)} loop />
        </CardContent>
      </Card>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) translateX(-50%); }
          50% { transform: translateY(-20px) translateX(-50%); }
        }
        
        @keyframes gradient {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        
        @keyframes bounce-in {
          0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
          50% { transform: scale(1.05) rotate(5deg); }
          70% { transform: scale(0.9) rotate(-2deg); }
          100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }
        
        .animate-gradient { background-size: 200% 200%; animation: gradient 3s ease infinite; }
        .animate-shimmer { animation: shimmer 2s infinite; }
        .animate-bounce-in { animation: bounce-in 1s ease-out; }
        .animate-pulse-slow { animation: pulse 3s infinite; }
        .animate-fade-in { animation: fadeIn 1s ease-in; }
        .animate-fade-in-up { animation: fadeInUp 1s ease-out; }
        .animate-fade-in-delayed { animation: fadeIn 2s ease-in; }
        .animate-typewriter { animation: typewriter 3s steps(40) 1s both; }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes fadeInUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes typewriter {
          from { width: 0; }
          to { width: 100%; }
        }
      `}</style>
    </div>
  )
}
